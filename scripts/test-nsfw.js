#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to test NSFW detection functionality
 * This script can be run to verify the NSFW detection is working correctly
 */

import { execSync } from 'child_process';
import path from 'path';
import fs from 'fs';
import { createRequire } from 'module';

const require = createRequire(import.meta.url);

console.log('🧪 Testing NSFW Detection Implementation...\n');

// Test 1: Run unit tests
console.log('1️⃣ Running unit tests...');
try {
  execSync('npm test -- src/__tests__/nsfw-detection.test.ts', {
    stdio: 'inherit',
    cwd: process.cwd()
  });
  console.log('✅ Unit tests passed!\n');
} catch (error) {
  console.log('❌ Unit tests failed!\n');
  console.error(error.message);
}

// Test 2: Check TypeScript compilation
console.log('2️⃣ Checking TypeScript compilation...');
try {
  execSync('npx tsc --noEmit', {
    stdio: 'inherit',
    cwd: process.cwd()
  });
  console.log('✅ TypeScript compilation successful!\n');
} catch (error) {
  console.log('❌ TypeScript compilation failed!\n');
  console.error(error.message);
}

// Test 3: Check if all required files exist
console.log('3️⃣ Checking required files...');
const requiredFiles = [
  'src/lib/nsfw-detection.ts',
  'src/hooks/use-nsfw-detection.ts',
  'src/lib/config/nsfw-config.ts',
  'src/components/features/upload/nsfw-feedback.tsx',
  'src/components/features/upload/cloudflare-single-image-uploader.tsx',
  'src/__tests__/nsfw-detection.test.ts',
  'src/app/(demo)/nsfw-demo/page.tsx'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - Missing!`);
    allFilesExist = false;
  }
});

if (allFilesExist) {
  console.log('\n✅ All required files exist!\n');
} else {
  console.log('\n❌ Some required files are missing!\n');
}

// Test 4: Check package.json dependencies
console.log('4️⃣ Checking dependencies...');
const packageJson = require('../package.json');
const requiredDeps = ['nsfwjs', '@tensorflow/tfjs'];

let allDepsInstalled = true;
requiredDeps.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    console.log(`✅ ${dep} - ${packageJson.dependencies[dep] || packageJson.devDependencies[dep]}`);
  } else {
    console.log(`❌ ${dep} - Not installed!`);
    allDepsInstalled = false;
  }
});

if (allDepsInstalled) {
  console.log('\n✅ All required dependencies are installed!\n');
} else {
  console.log('\n❌ Some required dependencies are missing!\n');
}

// Test 5: Lint the NSFW-related files
console.log('5️⃣ Linting NSFW-related files...');
try {
  const nsfwFiles = requiredFiles.filter(f => f.endsWith('.ts') || f.endsWith('.tsx')).join(' ');
  execSync(`npx eslint ${nsfwFiles} --fix`, {
    stdio: 'inherit',
    cwd: process.cwd()
  });
  console.log('✅ Linting passed!\n');
} catch (error) {
  console.log('⚠️ Linting completed with warnings/errors\n');
}

console.log('🎉 NSFW Detection testing complete!');
console.log('\n📋 Summary:');
console.log('- NSFW detection utilities implemented');
console.log('- React hooks for NSFW detection created');
console.log('- Configuration system set up');
console.log('- User feedback components ready');
console.log('- Image uploaders updated with NSFW detection');
console.log('- Unit tests written');
console.log('- Demo page created');
console.log('\n🚀 To test the implementation:');
console.log('1. Run: npm run dev');
console.log('2. Visit: http://localhost:3000/(demo)/nsfw-demo');
console.log('3. Upload test images to verify NSFW detection');
console.log('\n⚠️ Note: The NSFW model (~50MB) will be downloaded on first use.');
