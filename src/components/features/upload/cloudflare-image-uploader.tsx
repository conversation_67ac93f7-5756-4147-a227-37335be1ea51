"use client";

import { ImageIcon, PlusIcon, UploadIcon, XIcon } from "lucide-react";
import Image from "next/image";
import { useCallback, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { useCloudflareUpload } from "@/hooks/use-cloudflare-upload";
import { useNSFWDetection } from "@/hooks/use-nsfw-detection";
import { getNSFWConfigForUploadType, getNSFWErrorMessage } from "@/lib/config/nsfw-config";
import { cn } from "@/lib/utils";

export interface ImageUploadData {
  url: string;
  description?: string;
  key?: string;
}

interface CloudflareImageUploaderProps {
  /**
   * Initial images to display
   */
  initialImages?: ImageUploadData[];

  /**
   * Callback when images change
   */
  onChange: (images: ImageUploadData[]) => void;

  /**
   * Maximum number of images allowed
   */
  maxImages?: number;

  /**
   * Upload type for configuration
   */
  uploadType?: "property-image" | "job-image" | "profile-image";

  /**
   * Whether to show description input
   */
  showDescription?: boolean;

  /**
   * Custom class name
   */
  className?: string;
}

export function CloudflareImageUploader({
  initialImages = [],
  onChange,
  maxImages = 10,
  uploadType = "job-image",
  showDescription = true,
  className,
}: CloudflareImageUploaderProps) {
  const [images, setImages] = useState<ImageUploadData[]>(initialImages);
  const [currentDescription, setCurrentDescription] = useState("");
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { uploadFile, isUploading, progress, error } = useCloudflareUpload({
    uploadType,
    onSuccess: (url, key) => {
      const newImage: ImageUploadData = {
        url,
        key,
        description: showDescription ? currentDescription : undefined,
      };

      const newImages = [...images, newImage];
      setImages(newImages);
      onChange(newImages);
      setCurrentDescription("");
    },
  });

  const handleFileSelect = useCallback(
    async (files: FileList | null) => {
      if (!files || files.length === 0) return;

      // Check if we can add more images
      if (images.length >= maxImages) {
        return;
      }

      // Convert FileList to array and filter valid image files
      const fileArray = Array.from(files);
      const validFiles = fileArray.filter((file) =>
        file.type.startsWith("image/"),
      );

      if (validFiles.length === 0) {
        return;
      }

      // Calculate how many files we can actually upload
      const remainingSlots = maxImages - images.length;
      const filesToUpload = validFiles.slice(0, remainingSlots);

      // Upload files sequentially to avoid overwhelming the server
      for (const file of filesToUpload) {
        await uploadFile(file);
      }
    },
    [images.length, maxImages, uploadFile],
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  };

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      handleFileSelect(e.dataTransfer.files);
    },
    [handleFileSelect],
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
    onChange(newImages);
  };

  const updateImageDescription = (index: number, description: string) => {
    const newImages = [...images];
    const existingImage = newImages[index];
    if (existingImage) {
      newImages[index] = { ...existingImage, description };
      setImages(newImages);
      onChange(newImages);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Display existing images */}
      {images.length > 0 && (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {images.map((image, index) => (
            <div key={image.key} className="group relative">
              <div className="relative aspect-video overflow-hidden rounded-lg border bg-muted">
                <Image
                  src={image.url}
                  alt={image.description || `Image ${index + 1}`}
                  fill
                  className="object-cover"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2 opacity-0 transition-opacity group-hover:opacity-100"
                  onClick={() => removeImage(index)}
                >
                  <XIcon className="size-4" />
                </Button>
              </div>

              {showDescription && (
                <div className="mt-2">
                  <Input
                    placeholder="Image description"
                    value={image.description || ""}
                    onChange={(e) =>
                      updateImageDescription(index, e.target.value)
                    }
                    className="text-sm"
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Upload area */}
      {images.length < maxImages && (
        <div className="space-y-4">
          <button
            type="button"
            className={cn(
              "relative w-full rounded-lg border-2 border-dashed p-8 text-center transition-colors",
              dragActive
                ? "border-primary bg-primary/5"
                : "border-muted-foreground/25 hover:border-muted-foreground/50",
              isUploading && "pointer-events-none opacity-50",
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handleInputChange}
              className="sr-only"
              disabled={isUploading}
            />

            {isUploading ? (
              <div className="space-y-4">
                <UploadIcon className="mx-auto size-12 animate-pulse text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-muted-foreground text-sm">Uploading...</p>
                  <Progress
                    value={progress}
                    className="mx-auto w-full max-w-xs"
                  />
                  <p className="text-muted-foreground text-xs">{progress}%</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <ImageIcon className="mx-auto size-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="font-medium text-sm">
                    Drop images here or click to browse
                  </p>
                  <p className="text-muted-foreground text-xs">
                    PNG, JPG, WEBP up to 10MB • {maxImages - images.length}{" "}
                    remaining
                  </p>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={openFileDialog}
                  className="mt-4"
                >
                  <PlusIcon className="mr-2 size-4" />
                  Choose Image
                </Button>
              </div>
            )}
          </button>

          {showDescription && !isUploading && (
            <div className="space-y-2">
              <Label htmlFor="description">Description for next image</Label>
              <Input
                placeholder="Describe the image you're about to upload"
                value={currentDescription}
                onChange={(e) => setCurrentDescription(e.target.value)}
              />
            </div>
          )}

          {error && (
            <div className="rounded-md bg-destructive/15 p-3 text-destructive text-sm">
              {error}
            </div>
          )}
        </div>
      )}

      {images.length >= maxImages && (
        <div className="rounded-md bg-muted p-3 text-center text-muted-foreground text-sm">
          Maximum of {maxImages} images reached
        </div>
      )}
    </div>
  );
}
