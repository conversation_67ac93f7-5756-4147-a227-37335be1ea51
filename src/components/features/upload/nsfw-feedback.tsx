"use client";

import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Info, XCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import type { NSFWDetectionResult } from "@/lib/nsfw-detection";
import { getResultDescription } from "@/lib/nsfw-detection";
import { cn } from "@/lib/utils";

interface NSFWFeedbackProps {
  /**
   * NSFW detection result
   */
  result?: NSFWDetectionResult | null;

  /**
   * Whether NSFW detection is currently running
   */
  isChecking?: boolean;

  /**
   * Error message from NSFW detection
   */
  error?: string | null;

  /**
   * Whether to show detailed predictions
   */
  showDetails?: boolean;

  /**
   * Callback when user wants to retry
   */
  onRetry?: () => void;

  /**
   * Callback when user dismisses the feedback
   */
  onDismiss?: () => void;

  /**
   * Custom class name
   */
  className?: string;
}

/**
 * Component to display NSFW detection feedback to users
 */
export function NSFWFeedback({
  result,
  isChecking = false,
  error,
  showDetails = false,
  onRetry,
  onDismiss,
  className,
}: NSFWFeedbackProps) {
  // Don't render if there's nothing to show
  if (!isChecking && !result && !error) {
    return null;
  }

  // Show checking state
  if (isChecking) {
    return (
      <Alert className={cn("border-blue-200 bg-blue-50", className)}>
        <Info className="size-4 text-blue-600" />
        <AlertTitle className="text-blue-800">Checking Content</AlertTitle>
        <AlertDescription className="text-blue-700">
          <div className="space-y-2">
            <p>Verifying image content for appropriateness...</p>
            <Progress value={undefined} className="w-full" />
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Show error state
  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <XCircle className="size-4" />
        <AlertTitle>Content Verification Failed</AlertTitle>
        <AlertDescription>
          <div className="space-y-3">
            <p>{error}</p>
            {onRetry && (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRetry}
                  className="text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
                >
                  Try Again
                </Button>
                {onDismiss && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onDismiss}
                    className="text-destructive hover:bg-destructive/10"
                  >
                    Dismiss
                  </Button>
                )}
              </div>
            )}
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Show result
  if (result) {
    if (result.isNSFW) {
      return (
        <Alert variant="destructive" className={className}>
          <AlertTriangle className="size-4" />
          <AlertTitle>Inappropriate Content Detected</AlertTitle>
          <AlertDescription>
            <div className="space-y-3">
              <p>{getResultDescription(result)}</p>
              <p className="text-sm">
                Please choose a different image that complies with our content guidelines.
              </p>
              {showDetails && result.predictions.length > 0 && (
                <div className="space-y-2">
                  <p className="font-medium text-sm">Detection Details:</p>
                  <div className="space-y-1">
                    {result.predictions
                      .sort((a, b) => b.probability - a.probability)
                      .slice(0, 3)
                      .map((prediction, index) => (
                        <div
                          key={index}
                          className="flex justify-between text-xs"
                        >
                          <span>{prediction.className}</span>
                          <span>{Math.round(prediction.probability * 100)}%</span>
                        </div>
                      ))}
                  </div>
                </div>
              )}
              {onDismiss && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onDismiss}
                  className="text-destructive hover:bg-destructive/10"
                >
                  Dismiss
                </Button>
              )}
            </div>
          </AlertDescription>
        </Alert>
      );
    } else {
      return (
        <Alert className={cn("border-green-200 bg-green-50", className)}>
          <CheckCircle className="size-4 text-green-600" />
          <AlertTitle className="text-green-800">Content Verified</AlertTitle>
          <AlertDescription className="text-green-700">
            <div className="space-y-3">
              <p>{getResultDescription(result)}</p>
              {showDetails && result.predictions.length > 0 && (
                <div className="space-y-2">
                  <p className="font-medium text-sm">Detection Details:</p>
                  <div className="space-y-1">
                    {result.predictions
                      .sort((a, b) => b.probability - a.probability)
                      .slice(0, 3)
                      .map((prediction, index) => (
                        <div
                          key={index}
                          className="flex justify-between text-xs"
                        >
                          <span>{prediction.className}</span>
                          <span>{Math.round(prediction.probability * 100)}%</span>
                        </div>
                      ))}
                  </div>
                </div>
              )}
              {onDismiss && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onDismiss}
                  className="text-green-700 hover:bg-green-100"
                >
                  Dismiss
                </Button>
              )}
            </div>
          </AlertDescription>
        </Alert>
      );
    }
  }

  return null;
}

/**
 * Compact version of NSFW feedback for inline display
 */
export function NSFWFeedbackCompact({
  result,
  isChecking = false,
  error,
  className,
}: Pick<NSFWFeedbackProps, "result" | "isChecking" | "error" | "className">) {
  // Don't render if there's nothing to show
  if (!isChecking && !result && !error) {
    return null;
  }

  if (isChecking) {
    return (
      <div className={cn("flex items-center gap-2 text-blue-600 text-sm", className)}>
        <Info className="size-4" />
        <span>Checking content...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("flex items-center gap-2 text-destructive text-sm", className)}>
        <XCircle className="size-4" />
        <span>Content verification failed</span>
      </div>
    );
  }

  if (result?.isNSFW) {
    return (
      <div className={cn("flex items-center gap-2 text-destructive text-sm", className)}>
        <AlertTriangle className="size-4" />
        <span>Inappropriate content detected</span>
      </div>
    );
  }

  if (result && !result.isNSFW) {
    return (
      <div className={cn("flex items-center gap-2 text-green-600 text-sm", className)}>
        <CheckCircle className="size-4" />
        <span>Content verified</span>
      </div>
    );
  }

  return null;
}

/**
 * NSFW detection status indicator
 */
export function NSFWStatusIndicator({
  result,
  isChecking = false,
  error,
  size = "sm",
  className,
}: Pick<NSFWFeedbackProps, "result" | "isChecking" | "error" | "className"> & {
  size?: "sm" | "md" | "lg";
}) {
  const sizeClasses = {
    sm: "size-4",
    md: "size-5",
    lg: "size-6",
  };

  const iconSize = sizeClasses[size];

  if (isChecking) {
    return (
      <Info className={cn(iconSize, "text-blue-600 animate-pulse", className)} />
    );
  }

  if (error) {
    return (
      <XCircle className={cn(iconSize, "text-destructive", className)} />
    );
  }

  if (result?.isNSFW) {
    return (
      <AlertTriangle className={cn(iconSize, "text-destructive", className)} />
    );
  }

  if (result && !result.isNSFW) {
    return (
      <CheckCircle className={cn(iconSize, "text-green-600", className)} />
    );
  }

  return null;
}
