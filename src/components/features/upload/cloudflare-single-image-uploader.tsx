"use client";

import { ImageIcon, TrashIcon, UploadIcon } from "lucide-react";
import Image from "next/image";
import { useCallback, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useCloudflareUpload } from "@/hooks/use-cloudflare-upload";
import { cn } from "@/lib/utils";

interface CloudflareSingleImageUploaderProps {
  /**
   * Current image URL
   */
  value?: string;

  /**
   * Callback when image changes
   */
  onChange: (url: string | null) => void;

  /**
   * Upload type for configuration
   */
  uploadType?: "property-image" | "job-image" | "profile-image";

  /**
   * Custom class name
   */
  className?: string;

  /**
   * Aspect ratio for the upload area
   */
  aspectRatio?: "square" | "video" | "auto";

  /**
   * Size of the upload area
   */
  size?: "sm" | "md" | "lg";
}

export function CloudflareSingleImageUploader({
  value,
  onChange,
  uploadType = "job-image",
  className,
  aspectRatio = "video",
  size = "md",
}: CloudflareSingleImageUploaderProps) {
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { uploadFile, isUploading, progress, error } = useCloudflareUpload({
    uploadType,
    onSuccess: (url) => {
      onChange(url);
    },
  });

  const handleFileSelect = useCallback(
    async (files: FileList | null) => {
      if (!files || files.length === 0) return;

      const file = files[0];

      // Validate file type
      if (!file?.type.startsWith("image/")) {
        return;
      }

      await uploadFile(file);
    },
    [uploadFile],
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  };

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      handleFileSelect(e.dataTransfer.files);
    },
    [handleFileSelect],
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const removeImage = () => {
    onChange(null);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const sizeClasses = {
    sm: "h-32",
    md: "h-48",
    lg: "h-64",
  };

  const aspectClasses = {
    square: "aspect-square",
    video: "aspect-video",
    auto: "",
  };

  return (
    <div className={cn("space-y-2", className)}>
      <button
        type="button"
        className={cn(
          "relative overflow-hidden rounded-lg border-2 border-dashed transition-colors",
          aspectClasses[aspectRatio],
          sizeClasses[size],
          dragActive
            ? "border-primary bg-primary/5"
            : "border-muted-foreground/25 hover:border-muted-foreground/50",
          isUploading && "pointer-events-none opacity-50",
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleInputChange}
          className="sr-only"
          disabled={isUploading}
        />

        {value && !isUploading ? (
          // Show uploaded image
          <div className="group relative size-full">
            <Image
              src={value}
              alt="Uploaded image"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 transition-opacity group-hover:opacity-100" />
            <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity group-hover:opacity-100">
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={openFileDialog}
                >
                  Replace
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  onClick={removeImage}
                >
                  <TrashIcon className="size-4" />
                </Button>
              </div>
            </div>
          </div>
        ) : (
          // Show upload area
          <div className="flex size-full flex-col items-center justify-center p-4 text-center">
            {isUploading ? (
              <div className="space-y-4">
                <UploadIcon className="size-8 animate-pulse text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-muted-foreground text-sm">Uploading...</p>
                  <Progress value={progress} className="w-32" />
                  <p className="text-muted-foreground text-xs">{progress}%</p>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <ImageIcon className="size-8 text-muted-foreground" />
                <div className="space-y-1">
                  <p className="font-medium text-sm">
                    Drop image here or click to browse
                  </p>
                  <p className="text-muted-foreground text-xs">
                    PNG, JPG, WEBP up to 10MB
                  </p>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={openFileDialog}
                  className="mt-2"
                >
                  Choose Image
                </Button>
              </div>
            )}
          </div>
        )}
      </button>

      {error && (
        <div className="rounded-md bg-destructive/15 p-2 text-destructive text-sm">
          {error}
        </div>
      )}
    </div>
  );
}
