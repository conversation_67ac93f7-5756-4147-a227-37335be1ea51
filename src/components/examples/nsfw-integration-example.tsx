"use client";

import { useState } from "react";
import { CloudflareSingleImageUploader } from "@/components/features/upload/cloudflare-single-image-uploader";
import {
  NSFWFeedback,
  NSFWFeedbackCompact,
  NSFWStatusIndicator,
} from "@/components/features/upload/nsfw-feedback";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useNSFWDetection } from "@/hooks/use-nsfw-detection";
import { getNSFWConfigForUploadType } from "@/lib/config/nsfw-config";

/**
 * Example 1: Basic Integration with Existing Upload Component
 */
export function BasicNSFWExample() {
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Basic NSFW Detection</CardTitle>
      </CardHeader>
      <CardContent>
        <CloudflareSingleImageUploader
          value={imageUrl}
          onChange={setImageUrl}
          uploadType="job-image"
          enableNSFWDetection={true} // This enables NSFW detection
          aspectRatio="video"
          size="md"
        />
      </CardContent>
    </Card>
  );
}

/**
 * Example 2: Custom NSFW Detection with Manual Control
 */
export function CustomNSFWExample() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const { checkImage, isLoading, error, lastResult } = useNSFWDetection({
    config: getNSFWConfigForUploadType("property-image"), // Strict config
    onNSFWDetected: (result, file) => {
      console.log("NSFW content detected:", result);
      // Could show a toast notification here
    },
  });

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file || !file.type.startsWith("image/")) return;

    setSelectedFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => setImagePreview(e.target?.result as string);
    reader.readAsDataURL(file);

    // Check for NSFW content
    await checkImage(file);
  };

  const canProceed = lastResult && !lastResult.isNSFW && !isLoading && !error;

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Custom NSFW Detection
          <NSFWStatusIndicator
            result={lastResult}
            isChecking={isLoading}
            error={error}
          />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <input
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="block w-full text-sm"
        />

        {imagePreview && (
          <div className="relative">
            <img
              src={imagePreview}
              alt="Preview"
              className="h-32 w-full rounded object-cover"
            />
            <div className="absolute top-2 right-2">
              <NSFWStatusIndicator
                result={lastResult}
                isChecking={isLoading}
                error={error}
                size="md"
              />
            </div>
          </div>
        )}

        <NSFWFeedbackCompact
          result={lastResult}
          isChecking={isLoading}
          error={error}
        />

        <Button disabled={!canProceed} className="w-full">
          {isLoading
            ? "Checking..."
            : canProceed
              ? "Upload Image"
              : "Select Valid Image"}
        </Button>
      </CardContent>
    </Card>
  );
}

/**
 * Example 3: Detailed NSFW Feedback
 */
export function DetailedNSFWExample() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { checkImage, isLoading, error, lastResult, clearError } =
    useNSFWDetection({
      config: {
        threshold: 0.5,
        flaggedCategories: ["Porn", "Hentai", "Sexy"],
        modelType: "MobileNetV2",
      },
      preloadModel: true, // Preload for better UX
    });

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file || !file.type.startsWith("image/")) return;

    setSelectedFile(file);
    await checkImage(file);
  };

  const handleRetry = async () => {
    if (selectedFile) {
      clearError();
      await checkImage(selectedFile);
    }
  };

  return (
    <Card className="w-full max-w-lg">
      <CardHeader>
        <CardTitle>Detailed NSFW Feedback</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <input
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="block w-full text-sm"
        />

        <NSFWFeedback
          result={lastResult}
          isChecking={isLoading}
          error={error}
          showDetails={true}
          onRetry={handleRetry}
          onDismiss={() => {
            setSelectedFile(null);
            clearError();
          }}
        />
      </CardContent>
    </Card>
  );
}

/**
 * Example 4: Multiple Upload Types with Different Configs
 */
export function MultiConfigExample() {
  const [uploadType, setUploadType] = useState<
    "property-image" | "job-image" | "profile-image"
  >("job-image");
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  const uploadTypes = [
    {
      value: "property-image",
      label: "Property Image",
      description: "Strict filtering",
    },
    {
      value: "job-image",
      label: "Job Image",
      description: "Moderate filtering",
    },
    {
      value: "profile-image",
      label: "Profile Image",
      description: "Strict filtering",
    },
  ] as const;

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Multi-Config Example</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="font-medium text-sm">Upload Type:</label>
          <div className="grid grid-cols-1 gap-2">
            {uploadTypes.map((type) => (
              <Button
                key={type.value}
                variant={uploadType === type.value ? "default" : "outline"}
                size="sm"
                onClick={() => setUploadType(type.value)}
                className="justify-start"
              >
                <div className="text-left">
                  <div>{type.label}</div>
                  <div className="text-xs opacity-70">{type.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="font-medium text-sm">Current Config:</label>
            <Badge variant="outline">
              {getNSFWConfigForUploadType(uploadType).threshold * 100}%
              threshold
            </Badge>
          </div>
        </div>

        <CloudflareSingleImageUploader
          value={imageUrl}
          onChange={setImageUrl}
          uploadType={uploadType}
          enableNSFWDetection={true}
          aspectRatio="video"
          size="md"
        />
      </CardContent>
    </Card>
  );
}

/**
 * Example 5: Batch Processing with NSFW Filter
 */
export function BatchNSFWExample() {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [results, setResults] = useState<any[]>([]);

  const { checkImages, isLoading } = useNSFWDetection({
    onDetection: (result, file) => {
      setResults((prev) => [...prev, { file: file.name, result }]);
    },
  });

  const handleFilesSelect = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = Array.from(event.target.files || []);
    const imageFiles = files.filter((file) => file.type.startsWith("image/"));

    setSelectedFiles(imageFiles);
    setResults([]);

    if (imageFiles.length > 0) {
      await checkImages(imageFiles);
    }
  };

  const safeFiles = results.filter((r) => !r.result.isNSFW);
  const flaggedFiles = results.filter((r) => r.result.isNSFW);

  return (
    <Card className="w-full max-w-lg">
      <CardHeader>
        <CardTitle>Batch NSFW Processing</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <input
          type="file"
          accept="image/*"
          multiple
          onChange={handleFilesSelect}
          className="block w-full text-sm"
        />

        {isLoading && (
          <div className="text-center text-muted-foreground text-sm">
            Processing {selectedFiles.length} images...
          </div>
        )}

        {results.length > 0 && (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="text-center">
                <div className="font-medium text-green-600">
                  {safeFiles.length}
                </div>
                <div className="text-muted-foreground">Safe Images</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-red-600">
                  {flaggedFiles.length}
                </div>
                <div className="text-muted-foreground">Flagged Images</div>
              </div>
            </div>

            {flaggedFiles.length > 0 && (
              <div className="space-y-1">
                <div className="font-medium text-red-600 text-sm">
                  Flagged Files:
                </div>
                {flaggedFiles.map((item, index) => (
                  <div key={index} className="text-muted-foreground text-xs">
                    • {item.file} ({item.result.flaggedCategory})
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
