"use client";

import { useState } from "react";
import { CloudflareSingleImageUploader } from "@/components/features/upload/cloudflare-single-image-uploader";
import { NSFWFeedback } from "@/components/features/upload/nsfw-feedback";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useNSFWDetection } from "@/hooks/use-nsfw-detection";
import { NSFW_CONFIGS } from "@/lib/config/nsfw-config";
import type { NSFWDetectionResult } from "@/lib/nsfw-detection";

export default function NSFWDemoPage() {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [detectionResult, setDetectionResult] =
    useState<NSFWDetectionResult | null>(null);
  const [selectedConfig, setSelectedConfig] =
    useState<keyof typeof NSFW_CONFIGS>("moderate");

  const { checkImage, isLoading, error, preloadModel, isModelLoaded } =
    useNSFWDetection({
      config: NSFW_CONFIGS[selectedConfig],
      preloadModel: false,
      onDetection: (result) => {
        setDetectionResult(result);
      },
    });

  const handleImageSelect = async (file: File) => {
    setDetectionResult(null);
    try {
      await checkImage(file);
    } catch (err) {
      console.error("NSFW detection failed:", err);
    }
  };

  const handleFileInput = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith("image/")) {
      handleImageSelect(file);
    }
  };

  return (
    <div className="container mx-auto space-y-8 py-8">
      <div className="space-y-4 text-center">
        <h1 className="font-bold text-3xl">NSFW Detection Demo</h1>
        <p className="mx-auto max-w-2xl text-muted-foreground">
          Test the NSFW (Not Safe For Work) content detection system. Upload
          images to see how the AI classifies them for appropriateness.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle>Image Upload & Detection</CardTitle>
            <CardDescription>
              Upload an image to test NSFW detection. The system will analyze
              the content and provide a classification.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Model Status */}
            <div className="flex items-center justify-between rounded-lg bg-muted p-3">
              <span className="font-medium text-sm">Model Status:</span>
              <div className="flex items-center gap-2">
                <Badge variant={isModelLoaded ? "default" : "secondary"}>
                  {isModelLoaded ? "Loaded" : "Not Loaded"}
                </Badge>
                {!isModelLoaded && (
                  <Button size="sm" onClick={preloadModel} disabled={isLoading}>
                    Load Model
                  </Button>
                )}
              </div>
            </div>

            {/* Configuration Selection */}
            <div className="space-y-3">
              <label className="font-medium text-sm">
                Detection Sensitivity:
              </label>
              <div className="grid grid-cols-3 gap-2">
                {Object.entries(NSFW_CONFIGS).map(([key, config]) => (
                  <Button
                    key={key}
                    variant={selectedConfig === key ? "default" : "outline"}
                    size="sm"
                    onClick={() =>
                      setSelectedConfig(key as keyof typeof NSFW_CONFIGS)
                    }
                    className="text-xs"
                  >
                    {key.charAt(0).toUpperCase() + key.slice(1)}
                    <br />
                    <span className="text-xs opacity-70">
                      {Math.round(config.threshold * 100)}%
                    </span>
                  </Button>
                ))}
              </div>
            </div>

            <Separator />

            {/* File Input */}
            <div className="space-y-4">
              <label className="font-medium text-sm">
                Select Image for Testing:
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileInput}
                className="block w-full text-muted-foreground text-sm file:mr-4 file:rounded-md file:border-0 file:bg-primary file:px-4 file:py-2 file:font-medium file:text-primary-foreground file:text-sm hover:file:bg-primary/90"
              />
            </div>

            {/* Upload Component Demo */}
            <div className="space-y-4">
              <label className="font-medium text-sm">
                Or use the Upload Component:
              </label>
              <CloudflareSingleImageUploader
                value={selectedImage}
                onChange={setSelectedImage}
                uploadType="job-image"
                enableNSFWDetection={true}
                size="md"
                aspectRatio="video"
              />
            </div>
          </CardContent>
        </Card>

        {/* Results Section */}
        <Card>
          <CardHeader>
            <CardTitle>Detection Results</CardTitle>
            <CardDescription>
              View the NSFW detection results and classification details.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* NSFW Feedback */}
            <NSFWFeedback
              result={detectionResult}
              isChecking={isLoading}
              error={error}
              showDetails={true}
              onRetry={() => {
                // Retry logic would go here
                console.log("Retry requested");
              }}
              onDismiss={() => {
                setDetectionResult(null);
              }}
            />

            {/* Detailed Results */}
            {detectionResult && (
              <div className="space-y-4">
                <Separator />
                <div className="space-y-3">
                  <h4 className="font-medium">Classification Details</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Overall Result:</span>
                      <Badge
                        variant={
                          detectionResult.isNSFW ? "destructive" : "default"
                        }
                      >
                        {detectionResult.isNSFW ? "NSFW" : "Safe"}
                      </Badge>
                    </div>
                    {detectionResult.flaggedCategory && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Flagged Category:</span>
                        <Badge variant="outline">
                          {detectionResult.flaggedCategory}
                        </Badge>
                      </div>
                    )}
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Highest Confidence:</span>
                      <span className="font-mono text-sm">
                        {Math.round(detectionResult.highestProbability * 100)}%
                      </span>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-3">
                  <h4 className="font-medium">All Predictions</h4>
                  <div className="space-y-2">
                    {detectionResult.predictions
                      .sort((a, b) => b.probability - a.probability)
                      .map((prediction, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between rounded bg-muted p-2"
                        >
                          <span className="text-sm">
                            {prediction.className}
                          </span>
                          <div className="flex items-center gap-2">
                            <div className="h-2 w-20 rounded-full bg-background">
                              <div
                                className="h-2 rounded-full bg-primary"
                                style={{
                                  width: `${prediction.probability * 100}%`,
                                }}
                              />
                            </div>
                            <span className="w-12 text-right font-mono text-sm">
                              {Math.round(prediction.probability * 100)}%
                            </span>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            )}

            {/* Configuration Info */}
            <div className="space-y-3">
              <Separator />
              <h4 className="font-medium">Current Configuration</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Sensitivity:</span>
                  <span className="font-mono">{selectedConfig}</span>
                </div>
                <div className="flex justify-between">
                  <span>Threshold:</span>
                  <span className="font-mono">
                    {Math.round(NSFW_CONFIGS[selectedConfig].threshold * 100)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Flagged Categories:</span>
                  <span className="text-right font-mono">
                    {NSFW_CONFIGS[selectedConfig].flaggedCategories.join(", ")}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Model:</span>
                  <span className="font-mono">
                    {NSFW_CONFIGS[selectedConfig].modelType}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Use</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <ol className="list-inside list-decimal space-y-2 text-sm">
            <li>
              First, load the NSFW detection model by clicking "Load Model" if
              it's not already loaded.
            </li>
            <li>
              Choose a detection sensitivity level (Strict, Moderate, or
              Lenient).
            </li>
            <li>
              Upload an image using either the file input or the upload
              component.
            </li>
            <li>View the detection results and classification details.</li>
            <li>
              Try different sensitivity levels to see how they affect the
              results.
            </li>
          </ol>
          <div className="mt-4 rounded-lg bg-muted p-4">
            <p className="text-muted-foreground text-sm">
              <strong>Note:</strong> This is a demo environment. The NSFW
              detection model runs entirely in your browser and no images are
              sent to external servers for analysis.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
