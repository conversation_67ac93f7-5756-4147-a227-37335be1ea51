"use client";

import * as tf from "@tensorflow/tfjs";
import * as nsfwjs from "nsfwjs";

// NSFW prediction interface
export interface NSFWPrediction {
  className: string;
  probability: number;
}

// NSFW detection result
export interface NSFWDetectionResult {
  isNSFW: boolean;
  predictions: NSFWPrediction[];
  highestProbability: number;
  flaggedCategory: string | null;
}

// Configuration for NSFW detection
export interface NSFWConfig {
  threshold: number; // Threshold for considering content NSFW (0-1)
  flaggedCategories: string[]; // Categories to flag as NSFW
  modelType: "MobileNetV2" | "MobileNetV2Mid" | "InceptionV3";
}

// Default configuration
export const DEFAULT_NSFW_CONFIG: NSFWConfig = {
  threshold: 0.6, // 60% confidence threshold
  flaggedCategories: ["Porn", "Hentai"], // Categories to flag
  modelType: "MobileNetV2", // Fastest model for client-side use
};

// Global model cache
let modelCache: nsfwjs.NSFWJS | null = null;
let modelLoadingPromise: Promise<nsfwjs.NSFWJS> | null = null;

/**
 * Load the NSFW detection model
 * Uses caching to avoid loading the model multiple times
 */
export async function loadNSFWModel(
  config: NSFWConfig = DEFAULT_NSFW_CONFIG,
): Promise<nsfwjs.NSFWJS> {
  // Return cached model if available
  if (modelCache) {
    return modelCache;
  }

  // Return existing loading promise if model is being loaded
  if (modelLoadingPromise) {
    return modelLoadingPromise;
  }

  // Enable TensorFlow.js production mode for better performance
  tf.enableProdMode();

  // Start loading the model
  modelLoadingPromise = nsfwjs.load(config.modelType);

  try {
    modelCache = await modelLoadingPromise;
    return modelCache;
  } catch (error) {
    // Reset loading promise on error
    modelLoadingPromise = null;
    throw new Error(`Failed to load NSFW model: ${error}`);
  }
}

/**
 * Create an image element from a File object
 */
function createImageFromFile(file: File): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve(img);
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error("Failed to load image"));
    };

    img.src = url;
  });
}

/**
 * Classify an image file for NSFW content
 */
export async function classifyImage(
  file: File,
  config: NSFWConfig = DEFAULT_NSFW_CONFIG,
): Promise<NSFWDetectionResult> {
  try {
    // Validate file type
    if (!file.type.startsWith("image/")) {
      throw new Error("File must be an image");
    }

    // Load the model
    const model = await loadNSFWModel(config);

    // Create image element from file
    const img = await createImageFromFile(file);

    // Classify the image
    const predictions = await model.classify(img);

    // Process predictions
    const result = processPredictions(predictions, config);

    return result;
  } catch (error) {
    throw new Error(`NSFW classification failed: ${error}`);
  }
}

/**
 * Process NSFW predictions and determine if content should be flagged
 */
function processPredictions(
  predictions: NSFWPrediction[],
  config: NSFWConfig,
): NSFWDetectionResult {
  // Find the highest probability prediction
  const highestPrediction = predictions.reduce((prev, current) =>
    prev.probability > current.probability ? prev : current,
  );

  // Check if any flagged category exceeds the threshold
  const flaggedPrediction = predictions.find(
    (pred) =>
      config.flaggedCategories.includes(pred.className) &&
      pred.probability >= config.threshold,
  );

  const isNSFW = !!flaggedPrediction;
  const flaggedCategory = flaggedPrediction?.className || null;

  return {
    isNSFW,
    predictions,
    highestProbability: highestPrediction.probability,
    flaggedCategory,
  };
}

/**
 * Batch classify multiple images
 */
export async function classifyImages(
  files: File[],
  config: NSFWConfig = DEFAULT_NSFW_CONFIG,
): Promise<NSFWDetectionResult[]> {
  const results: NSFWDetectionResult[] = [];

  // Process images sequentially to avoid overwhelming the browser
  for (const file of files) {
    try {
      const result = await classifyImage(file, config);
      results.push(result);
    } catch {
      // Push error result for failed classifications
      results.push({
        isNSFW: false, // Default to safe if classification fails
        predictions: [],
        highestProbability: 0,
        flaggedCategory: null,
      });
    }
  }

  return results;
}

/**
 * Check if the NSFW model is loaded
 */
export function isModelLoaded(): boolean {
  return modelCache !== null;
}

/**
 * Preload the NSFW model for better performance
 */
export async function preloadNSFWModel(
  config: NSFWConfig = DEFAULT_NSFW_CONFIG,
): Promise<void> {
  try {
    await loadNSFWModel(config);
  } catch (error) {
    console.warn("Failed to preload NSFW model:", error);
  }
}

/**
 * Clear the model cache (useful for testing or memory management)
 */
export function clearModelCache(): void {
  if (modelCache) {
    // Dispose of TensorFlow.js tensors to free memory
    modelCache.model.dispose();
    modelCache = null;
  }
  modelLoadingPromise = null;
}

/**
 * Get human-readable description of NSFW detection result
 */
export function getResultDescription(result: NSFWDetectionResult): string {
  if (!result.isNSFW) {
    return "Image appears to be safe for work";
  }

  const confidence = Math.round(result.highestProbability * 100);
  return `Image may contain inappropriate content (${result.flaggedCategory}, ${confidence}% confidence)`;
}
