import type { NSFWConfig } from "@/lib/nsfw-detection";

/**
 * NSFW detection configuration for different contexts
 */
export const NSFW_CONFIGS = {
  // Strict configuration for public-facing content
  strict: {
    threshold: 0.4, // Lower threshold = more sensitive
    flaggedCategories: ["Porn", "Hentai", "Sexy"],
    modelType: "MobileNetV2",
  } as NSFWConfig,

  // Moderate configuration for general use
  moderate: {
    threshold: 0.6, // Balanced threshold
    flaggedCategories: ["Porn", "Hentai"],
    modelType: "MobileNetV2",
  } as NSFWConfig,

  // Lenient configuration for less restrictive contexts
  lenient: {
    threshold: 0.8, // Higher threshold = less sensitive
    flaggedCategories: ["Porn"],
    modelType: "MobileNetV2",
  } as NSFWConfig,

  // High-accuracy configuration using InceptionV3 model
  highAccuracy: {
    threshold: 0.6,
    flaggedCategories: ["Porn", "Hentai"],
    modelType: "InceptionV3",
  } as NSFWConfig,
} as const;

/**
 * Configuration for different upload types
 */
export const UPLOAD_TYPE_NSFW_CONFIG = {
  "property-image": NSFW_CONFIGS.strict, // Property images should be family-friendly
  "job-image": NSFW_CONFIGS.moderate, // Job images can be slightly more lenient
  "profile-image": NSFW_CONFIGS.strict, // Profile images should be appropriate
} as const;

/**
 * Get NSFW configuration for a specific upload type
 */
export function getNSFWConfigForUploadType(
  uploadType: keyof typeof UPLOAD_TYPE_NSFW_CONFIG,
): NSFWConfig {
  return UPLOAD_TYPE_NSFW_CONFIG[uploadType] || NSFW_CONFIGS.moderate;
}

/**
 * Environment-based configuration
 */
export function getNSFWConfigForEnvironment(): NSFWConfig {
  // Use stricter settings in production
  if (process.env.NODE_ENV === "production") {
    return NSFW_CONFIGS.strict;
  }

  // Use moderate settings in development
  return NSFW_CONFIGS.moderate;
}

/**
 * User role-based configuration
 */
export function getNSFWConfigForUserRole(role: string): NSFWConfig {
  switch (role) {
    case "admin":
      return NSFW_CONFIGS.lenient; // Admins can see more content
    case "contractor":
      return NSFW_CONFIGS.moderate; // Contractors need moderate filtering
    case "homeowner":
      return NSFW_CONFIGS.strict; // Homeowners get strict filtering
    default:
      return NSFW_CONFIGS.strict; // Default to strict for unknown roles
  }
}

/**
 * Combined configuration that considers multiple factors
 */
export function getOptimalNSFWConfig(params: {
  uploadType?: keyof typeof UPLOAD_TYPE_NSFW_CONFIG;
  userRole?: string;
  environment?: string;
}): NSFWConfig {
  const { uploadType, userRole, environment } = params;

  // Start with upload type configuration
  let config = uploadType
    ? getNSFWConfigForUploadType(uploadType)
    : NSFW_CONFIGS.moderate;

  // Adjust based on user role (make it stricter if needed)
  if (userRole) {
    const roleConfig = getNSFWConfigForUserRole(userRole);
    if (roleConfig.threshold < config.threshold) {
      config = { ...config, threshold: roleConfig.threshold };
    }
    // Combine flagged categories
    const combinedCategories = Array.from(
      new Set([...config.flaggedCategories, ...roleConfig.flaggedCategories]),
    );
    config = { ...config, flaggedCategories: combinedCategories };
  }

  // Adjust based on environment
  if (environment === "production") {
    const envConfig = getNSFWConfigForEnvironment();
    if (envConfig.threshold < config.threshold) {
      config = { ...config, threshold: envConfig.threshold };
    }
  }

  return config;
}

/**
 * Error messages for NSFW detection
 */
export const NSFW_ERROR_MESSAGES = {
  inappropriate_content:
    "This image contains inappropriate content and cannot be uploaded.",
  classification_failed: "Unable to verify image content. Please try again.",
  model_load_failed:
    "Content verification is temporarily unavailable. Please try again later.",
  file_too_large: "Image is too large for content verification.",
  invalid_file_type: "File type not supported for content verification.",
} as const;

/**
 * Get user-friendly error message for NSFW detection
 */
export function getNSFWErrorMessage(
  errorType: keyof typeof NSFW_ERROR_MESSAGES,
): string {
  return NSFW_ERROR_MESSAGES[errorType];
}

/**
 * Configuration for model preloading
 */
export const MODEL_PRELOAD_CONFIG = {
  // Preload on app initialization
  preloadOnInit: process.env.NODE_ENV === "production",

  // Preload when user navigates to upload pages
  preloadOnUploadPage: true,

  // Cache duration in milliseconds (1 hour)
  cacheTimeout: 60 * 60 * 1000,
} as const;
